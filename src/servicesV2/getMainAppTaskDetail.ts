import request from './request'
import { API_DEFAULT } from '../constantsV2/apiConfig'
import { MainAppTaskDetailRequest, MainAppTaskDetailResponse } from '../typesV2/mainAppTaskDetail'
// export interface MainAppTaskDetailRequest {
//     sceneId: number; // 场景ID（首页为1，福利中心为2）
//     installMainApp: boolean; // 是否安装主APP
// }

// export interface MainAppTaskDetailResponse {
//     taskId?: number; // 任务ID
//     coinsNum?: number; // 金币数量
//     showTask: boolean; // 是否展示任务
//     modelTitle?: string; // 模块标题（福利页独有）
//     jumpUrl?: string; // 跳转链接
//     title?: string; // 主标题
//     subTitle?: string; // 副标题
//     buttonText?: string; // 按钮文案
//     taskStatus?: number; // 任务状态 0-未完成 1-已完成 2-已领取
//     icon?: string; // 图标地址（福利页独有）
// }

/**
 * 查询导流主APP任务状态
 * @param params 请求参数
 * @returns Promise<MainAppTaskDetailResponse>
 */
export const getMainAppTaskDetail = async (
    params: MainAppTaskDetailRequest
): Promise<MainAppTaskDetailResponse> => {
    console.log('getMainAppTaskDetail', API_DEFAULT)
    const response = await request<MainAppTaskDetailResponse>({
        ...API_DEFAULT,
        url: 'speed/web-earn/mainAppExchange/getTaskDetail',
        // option: {
        //     method: 'post',
        //     data: params,
        // },

        option: {
            data: JSON.stringify(params),
            method: 'post',
            headers: {
                'Content-Type': 'application/json',
            },
        },
    });

    console.log('getMainAppTaskDetail response', response)

    return response?.data || {} as MainAppTaskDetailResponse;
};

export default getMainAppTaskDetail;