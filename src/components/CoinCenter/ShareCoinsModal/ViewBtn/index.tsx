import { Text, Touch } from '@xmly/rn-components';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Animated } from 'react-native';
import type { ViewStyle, TextStyle, LayoutChangeEvent } from 'react-native';
import { darkTheme } from '../theme/banner';
import { useAtomValue } from 'jotai';
// import taskAtom from '../store';
import usePlayAd from '../hook/usePlayAd';
import LinearGradient from 'react-native-linear-gradient';
import { coinsModalShowAtom } from '../store';
import useTaskCenterLoaded from 'hooks/useTaskCenterLoaded';
import btnThemeStyleAtom from '../theme/btn';
import { ActionType } from '../utils';
import { shareConinsTaskAtom } from '../../ShareCoins/store';

export enum ViewBtnLocation {
  Banner = 'banner',
  Modal = 'modal',
}

interface Props {
  btnStyle: ViewStyle | ViewStyle[];
  btnBg: typeof darkTheme.btnBg;
  disabledBtnBg?: typeof darkTheme.disabledBtnBg;
  btnTextStyle: TextStyle;
  btnDisabledStyle?: ViewStyle;
  btnDisabledTextStyle?: TextStyle;
  onPress?: (btnText: string, action: ActionType) => void;
  position?: ViewBtnLocation;
  animationInitScale?: number;
  animationToScale?: number;
  noAnimation?: boolean;
}

export default function ViewBtn(props: Props) {
  const {
    btnStyle,
    btnBg,
    disabledBtnBg,
    btnDisabledStyle,
    btnTextStyle,
    btnDisabledTextStyle,
    onPress,
    position,
    animationInitScale = 1,
    animationToScale = 1.05,
    noAnimation = false
  } = props;
  // const taskInfo = useAtomValue(taskAtom);

  const taskInfo = useAtomValue(shareConinsTaskAtom)
  // const rewards = taskInfo?.awardInfo;

  // const { added, addToCalendar } = useCalendar();
  const playAd = usePlayAd();
  const scale = useRef(new Animated.Value(animationInitScale)).current;
  let left = useRef(new Animated.Value(0)).current;
  const opacity = useRef(new Animated.Value(1)).current;
  const cashModalShow = useAtomValue(coinsModalShowAtom);
  const taskCenterLoaded = useTaskCenterLoaded();
  const [width, setWidth] = useState(0);
  const themeStyle = useAtomValue(btnThemeStyleAtom);

  const disabled = !taskInfo?.enableReward;
  const animation = useMemo(() => {
    return width && !noAnimation ? Animated.loop(
      Animated.sequence([
        Animated.timing(opacity, {
          toValue: 1,
          duration: 0,
          useNativeDriver: true,
        }),
        Animated.parallel([
          Animated.timing(scale, {
            toValue: animationToScale,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(left, {
            toValue: width + 67,
            duration: 800,
            useNativeDriver: true,
          }),
        ]),
        Animated.timing(opacity, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
        Animated.timing(left, {
          toValue: 0,
          duration: 0,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: animationInitScale,
          duration: 400,
          useNativeDriver: true,
        }),
        Animated.timing(scale, {
          toValue: animationInitScale,
          duration: 1400,
          useNativeDriver: true,
        })
      ])
    ) : null
  }, [width, noAnimation]);

  function pressHandle() {
    let action: ActionType = ActionType.Video;
    let btnText = taskInfo?.buttonText ?? '';
    if (taskInfo?.enableReward) {
      playAd.handleWatchAd();
    }
    onPress?.(btnText, action);
  }

  useEffect(() => {
    if (animation) {
      if (!disabled) {
        if (cashModalShow && position === ViewBtnLocation.Modal) {
          animation?.start();
        } else if (!cashModalShow && position === ViewBtnLocation.Banner && taskCenterLoaded) {
          animation?.start();
        }
      } else if (animation) {
        animation?.reset();
        left.setValue(0);
      }
    }
  }, [cashModalShow, position, taskCenterLoaded, disabled, animation])

  function onLayout(e: LayoutChangeEvent) {
    setWidth(e.nativeEvent.layout.width);
  }

  return taskInfo ? <Touch
    style={[btnStyle, disabled ? btnDisabledStyle : null]}
    onPress={pressHandle}
    activeOpacity={.5}
    disabled={disabled}
    onLayout={onLayout}
  >
    <Animated.View style={[{ width: '100%', height: '100%', position: 'absolute', }, { transform: [{ scale }] }]}>
      <LinearGradient
        {...disabled ? disabledBtnBg || btnBg : btnBg}
        style={{
          width: '100%',
          height: '100%',
          position: 'absolute',
          borderRadius: 139,
          overflow: 'hidden',
        }}
        useAngle={true}
      >
        <Animated.Image
          source={{ uri: themeStyle.light }}
          style={[{
            height: '100%',
            aspectRatio: 67 / 46,
            position: 'absolute',
            left: -67,
            opacity,
            transform: [{ translateX: left }],
          }]} />
      </LinearGradient>
    </Animated.View>
    <Text style={[btnTextStyle, disabled ? btnDisabledTextStyle : null]}>{taskInfo?.buttonText}</Text>
  </Touch> : null;
}