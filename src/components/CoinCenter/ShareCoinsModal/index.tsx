import { BetterImage, Touch } from "@xmly/rn-components";
import { useEffect, useState, useRef } from "react";
import { containerHeight, containerWidth, getStyles } from "./style";
import { Text, View, Image, useColorScheme, Platform } from "react-native";
import { useAtomValue, useSetAtom } from "jotai";
import Pop from "../../../componentsV2/common/Pop";
import React from "react";
import StepAward from "./StepAward";
import GradientText from "./GradientText";
import themeStyleAtom from "./theme/modal";
import useShowCashModal from "./hook/useShowCoinsModal";
import ViewBtn, { ViewBtnLocation } from "./ViewBtn";
import { coinsModalShowAtom } from "./store";
import { modalClickReport, AreaType, ActionType } from "./utils";
import { shareConinsTaskAtom } from "../ShareCoins/store";
import usePlayAd from "./hook/usePlayAd";
import { icon_sharecoins_close } from "appImagesV2/share_coins_modal";
import { pendingRewardModalAtom, initialState } from "atom/listenTaskModal";
import { showRewardModalAtom } from "atom/listenTaskModal";

function ShareCoinsModal() {
  const themeStyles = useAtomValue(themeStyleAtom);
  const styles = getStyles(themeStyles);
  const { close } = useShowCashModal();
  const showModal = useAtomValue(coinsModalShowAtom);
  const shareConinsTaskInfo = useAtomValue(shareConinsTaskAtom)

  const setModalInfo = useSetAtom(showRewardModalAtom);
  const setPendingRewardModal = useSetAtom(pendingRewardModalAtom);
  const pendingRewardModal = useAtomValue(pendingRewardModalAtom);
  const timerRef = useRef<NodeJS.Timeout | null>(null);


  const [isBtnDisabled, setIsBtnDisabled] = useState(false);

  const playAd = usePlayAd();
  const modalInfo = shareConinsTaskInfo?.shareCoinsModalInfo;
  const isDark = useColorScheme() === 'dark';
  const incentiveIconUrl = isDark ? modalInfo?.incentiveDarkIcon : modalInfo?.incentiveLightIcon;
  function click(btnText: string, action: ActionType) {
    setIsBtnDisabled(true);
    setTimeout(() => setIsBtnDisabled(false), 1500);
    modalClickReport({ btnText, area: AreaType.Button, action });
  }

  function stepPressReport() {
    modalClickReport({ btnText: '今日可领', area: AreaType.Item, action: ActionType.Video });
  }

  // 当 ShareCoinsModal 关闭时，检查是否有待展示的奖励弹窗
useEffect(() => {
  if (showModal === false && pendingRewardModal) {
    setModalInfo(pendingRewardModal);
    setPendingRewardModal(null);
  }
}, [showModal, pendingRewardModal]);

useEffect(() => {
  // 任务完成时，3秒后自动关闭弹窗
  if (
    showModal &&
    !shareConinsTaskInfo.enableReward
  ) {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }

    timerRef.current = setTimeout(() => {
      close();
    }, 3000);
  }
  // 清理定时器
  return () => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };
}, [showModal, shareConinsTaskInfo.enableReward]);


  return showModal ? <Pop
    handleClose={close}
  >
    <View style={styles.container}>
      <Image
        resizeMode={'cover'}
        style={styles.bg}
        source={themeStyles.modalBg}
        // imgWidth={containerWidth}
        // imgHeight={containerHeight}
      />
     
     {incentiveIconUrl ? (
      <View style={styles.markIcon}>
        <BetterImage
          style={styles.markIconImage}
          imgWidth={80}
          imgHeight={70}
          source={{uri: incentiveIconUrl}}
        />
      </View>
      ) : null}
      <Touch disabled={isBtnDisabled} onPress={() => {
        if(shareConinsTaskInfo.alreadyTimes!=shareConinsTaskInfo.totalTimes ) {
          setIsBtnDisabled(true);
          playAd.handleWatchAd();
          modalClickReport({ btnText: modalInfo?.shareCoinsModalTitle || '瓜分弹窗banner', area: AreaType.Button, action: ActionType.Banner });
        }
        setTimeout(() => setIsBtnDisabled(false), 1500);
        }}>
        <View style={styles.head}>
            {Platform.OS == 'android' ? (
              <Text style={styles.title}>{modalInfo?.shareCoinsModalTitle}</Text>
              // <GradientTextBySvg
              //   text={modalInfo?.shareCoinsModalTitle}
              //   style={styles.title}
              //   colors={themeStyles.titleGradientColors}
              //   locations={themeStyles.titleGradientLocations}
              // />
            ) :
            (<GradientText
              text={modalInfo?.shareCoinsModalTitle}
              style={styles.title}
              colors={themeStyles.titleGradientColors}
              locations={themeStyles.titleGradientLocations}
            />)}
            <Text style={styles.subTitle}>{modalInfo?.shareCoinsModalSubTitle}</Text>
        </View>
      </Touch>
      
      <View style={[styles.body]}>
        <StepAward onPressReport={stepPressReport} />
        <View style={styles.btnWrapper}>
          <ViewBtn
            btnBg={themeStyles.btnBg}
            btnDisabledStyle={styles.btnDisabled}
            btnStyle={styles.btn}
            btnTextStyle={styles.btnTitle}
            onPress={click}
            position={ViewBtnLocation.Modal}
          />
        </View>
      </View>
      <Touch style={styles.close} onPress={close}>
        <Image
          // imgWidth={13}
          // imgHeight={13}
          style={styles.closeIcon}
          source={icon_sharecoins_close}
        />
      </Touch>
    </View>
  </Pop> : null
}

export default React.memo(ShareCoinsModal);