import React from 'react';
import { Text, Platform } from 'react-native';
import MaskedView from '@react-native-community/masked-view';
import LinearGradient from 'react-native-linear-gradient';
// import Svg, { Text as SvgText, Defs, LinearGradient as SvgLinearGradient, Stop } from 'react-native-svg';

export default function GradientText({ text, style, colors, locations }: { text: string | undefined, style: any, colors: string[], locations: number[] }) {
  return (
    <MaskedView style={{ width: 132, height: 33, backgroundColor: 'transparent' }}
      maskElement={
        <Text
          style={[
            style,
            { backgroundColor: 'transparent' }
          ]}
        >
          {text}
        </Text>
      }
    >
      <LinearGradient
        colors={colors}
        locations={locations}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={{ flex: 1, backgroundColor: 'transparent' }}
      />
    </MaskedView>
  );
}

// export function GradientTextBySvg({
//     text,
//     width = 200,
//     height = 40,
//     fontSize = 22,
//     colors = ['#FF7E5F', '#FEB47B'],
//     locations = [0, 1],
//     style = {},
//     fontWeight = 'bold',
//     fontFamily = Platform.OS === 'ios' ? 'PingFangSC-Semibold' : 'PingFang SC',
//   }) {
//     return (
//       <Svg width={width} height={height}>
//         <Defs>
//           <SvgLinearGradient id="grad" x1="0" y1="0" x2="1" y2="0" >
//             {colors.map((color, idx) => (
//               <Stop
//                 key={color + idx}
//                 offset={locations[idx]}
//                 stopColor={color}
//                 stopOpacity="1"
//               />
//             ))}
//           </SvgLinearGradient>
//         </Defs>
//         <SvgText
//           fill="url(#grad)"
//           x={width / 2}
//           y={height * 0.75}
//           fontSize={fontSize}
//           fontWeight={fontWeight}
//           fontFamily={fontFamily}
//           textAnchor="middle"
//           {...style}
//         >
//           {text}
//         </SvgText>
//       </Svg>
//     );
// }