import { Text, Touch } from "@xmly/rn-components";
import { getStyles } from "./style";
import { StyleSheet, View, Image } from "react-native";
import { useAtomValue } from "jotai";
// import awardsAtom from "../store";
import awardThemeStyleAtom from "../theme/award";
import LinearGradient from 'react-native-linear-gradient';
import React from "react";
import type { darkTheme } from "../theme/award";
import usePlayAd from "../hook/usePlayAd";
import { Toast } from "@xmly/rn-sdk";
import { AwardStatus } from "services/welfare/shareCoinsTask";
import { shareConinsTaskAtom } from "../../ShareCoins/store";
import { icon_sharecoins_tag } from '../../../../appImagesV2/share_coins_modal'


interface Props {
  customThemeStyles?: Partial<typeof darkTheme>;
  fold?: boolean;
  onPressReport?: () => void;
}

function StepAward(props: Props) {
  const { customThemeStyles = {}, fold, onPressReport } = props;
  const awardThemeStyles = useAtomValue(awardThemeStyleAtom);
  const themeStyles = { ...awardThemeStyles, ...customThemeStyles };
  const styles = getStyles(themeStyles);
  // const awards = useAtomValue(awardsAtom);

  const playAd = usePlayAd();

  const shareConinsTaskInfo = useAtomValue(shareConinsTaskAtom)
  const awards = shareConinsTaskInfo?.shareCoinsModalInfo?.dailyCoinsAwardDetail;

  // console.log('shareConinsTaskInfo>>>>>>>>>>>>>>>>>>>>>>>', shareConinsTaskInfo)
  if (!awards) {
    return null;
  }

  const todayIndex = Array.isArray(awards) ? awards.findIndex(award => award.today) : -1;
  // 今天不显示大额布局，除非今天是最后一天
  const firstBigStep = Array.isArray(awards) ? awards.findIndex((award, index) => award.supriseAward && index !== 3 && award.status !== AwardStatus.GOT && (!award.today || (index === awards.length - 1 && award.today))) : 0; 
  const todayCompleted = awards?.[todayIndex]?.status === AwardStatus.GOT;

  function play(status: AwardStatus, index: number) {
    if (index === todayIndex) {
      if (status === AwardStatus.CAN_GET) {
        playAd.handleWatchAd();
        onPressReport?.();
      }
    } else {
      Toast.info('未到领取时间');
    }
  }

  return <View
    style={[styles.steps, fold ? styles.foldSteps : null]}
  >
    {Array.isArray(awards) ? awards.map((award, index) => {
      const isBig = index === firstBigStep && (!award.today || (index === awards.length - 1 && award.today));
      let headBg: typeof darkTheme.stepHeadBg;
      let stepBodyStyle, stepBgStyle, stepHeadTextStyle, amountTextStyle, stepHeadStyle, stepAtMostTextStyle;
      const headText = award.today ? '今日可领' : (todayCompleted && index === todayIndex + 1) ? '明日可领': `第${award.stepNo}天`;

      if (award.status === AwardStatus.GOT) {
        headBg = themeStyles.stepHeadBgFinish;
        stepBgStyle = StyleSheet.flatten([styles.stepBg, styles.stepBgFinish]);
        stepBodyStyle = StyleSheet.flatten([styles.step, styles.stepFinish]);
        stepHeadTextStyle = StyleSheet.flatten([styles.stepHeadText, styles.stepHeadTextFinish]);
        stepAtMostTextStyle = StyleSheet.flatten([styles.atMost, styles.atMostFinish]);
      } else if (award.today) {
        amountTextStyle = StyleSheet.flatten([styles.amountText, styles.amountTextActive]);
        headBg = themeStyles.stepHeadBgActive;
        stepBgStyle = StyleSheet.flatten([styles.stepBg, styles.stepBgActive]);
        stepBodyStyle = StyleSheet.flatten([styles.step, styles.stepActive]);
        stepHeadTextStyle = StyleSheet.flatten([styles.stepHeadText, styles.stepHeadTextActive]);
        stepAtMostTextStyle = StyleSheet.flatten([styles.atMost, styles.atMostActive]);
      } else {
        amountTextStyle = styles.amountText;
        headBg = themeStyles.stepHeadBg;
        stepBgStyle = styles.stepBg;
        stepBodyStyle = styles.step;
        stepHeadTextStyle = styles.stepHeadText;
        stepHeadStyle = styles.stepHeadBg;
        stepAtMostTextStyle = styles.atMost;
      }

      return <Touch
        style={[
          stepBodyStyle,
          { flexBasis: isBig ? '47%' : '22%' },
          { zIndex: awards.length - index }
        ]}
        key={index}
        onPress={() => play(award.status, index)}
        disabled={award.status === AwardStatus.GOT}
      >
        <View style={stepBgStyle}>
          <LinearGradient
            style={[styles.stepHead, stepHeadStyle]}
            useAngle={true}
            {...headBg}
          />
        </View>
        <View
          style={styles.stepHead}
        >
          {award.today ?
            <Text style={stepHeadTextStyle}>
              {headText}
            </Text>
            :
            award.supriseAward ?
              <View style={styles.stepHeadTextWrap}>
                <Text style={stepHeadTextStyle}>
                  {headText}
                </Text>
                {award.status !== AwardStatus.GOT ?
                  <Image
                    // imgWidth={32}
                    // imgHeight={16}
                    source={icon_sharecoins_tag}
                    style={[styles.bigAward, headText.length === 4 ? { left: 52 } : null]}
                  />
                  :
                  null}
              </View>
              :
              <Text style={stepHeadTextStyle}>
                {headText}
              </Text>
          }
        </View>
        {award.status === AwardStatus.GOT ?
          <Image
            source={themeStyles.stepFinishBg}
            // imgWidth={18.5}
            // imgHeight={12.5}
            style={styles.finishBg}
          />
          :
          <View style={styles.stepContent}>
            <Text style={stepAtMostTextStyle}>最高</Text>
            <View style={styles.amount}>
              <Text style={[styles.amountNumber, amountTextStyle]}>{award.amountText}</Text>
              {/* <Text style={[styles.amountUnit, amountTextStyle]}>元</Text> */}
            </View>
          </View>
        }
      </Touch>
    }) : null}
  </View>
}

export default React.memo(StepAward);