import { StyleSheet } from 'react-native';
import type { darkTheme } from '../theme/award';
import { gap } from '../style';

export const stepHeight = 64;

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  steps: {
    display: 'flex',
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: '100%',
    height: stepHeight * 2 + gap * 2,
    justifyContent: 'space-around'
  },
  foldSteps: {
    height: stepHeight + gap,
  },
  step: {
    height: stepHeight,
    borderRadius: 8,
    marginVertical: gap / 2,
    backgroundColor: theme.bodyBgColor,
    position: 'relative',
  },
  stepActive: {
    backgroundColor: theme.bodyBgColorActive,
  },
  stepFinish: {
    backgroundColor: theme.bodyBgColorFinish,
  },
  stepBg: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    borderRadius: 8,
    borderWidth: 1,
    borderStyle: 'solid',
    borderColor: theme.stepBorderColor,
    overflow: 'hidden',
  },
  stepBgActive: {
    borderColor: theme.stepBorderColorActive,
    borderWidth: 3,
  },
  stepBgFinish: {
    borderColor: theme.stepBorderColorFinish,
  },
  stepHead: {
    height: 22,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  stepHeadBg: {
    opacity: theme.stepHeadBgOpacity,
  },
  stepHeadText: {
    fontFamily: 'PingFang SC',
    fontSize: 12,
    fontWeight: '500',
    color: theme.stepTextColor,
    top: 1
  },
  stepHeadTextActive: {
    color: theme.stepTextColorActive,
  },
  stepHeadTextFinish: {
    color: theme.stepTextColorFinish,
  },
  stepHeadTextWrap: {
    position: 'relative',
  },
  bigAward: {
    width: 32,
    height: 24,
    position: 'absolute',
    top: '-75%',
    left: 32,
  },
  stepContent: {
    position: 'relative',
  },
  atMost: {
    fontFamily: 'PingFang SC',
    fontSize: 8,
    color: '#D2D1EA',
    textAlign: 'right',
    position: 'absolute',
    right: 4,
    top: 2,
  },
  atMostFinish: {
    color: '#FFF'
  },
  atMostActive: {
    color: '#FFB5B5'
  },
  amount: {
    alignItems: 'flex-end',
    justifyContent: 'center',
    flexDirection: 'row',
    marginTop: 7
  },
  amountNumber: {
    fontFamily: 'XmlyNumber',
    fontWeight: 'bold',
    fontSize: 15,
    lineHeight: 32,
  },
  amountUnit: {
    fontFamily: 'PingFang SC',
    fontSize: 11,
    lineHeight: 27,
    fontWeight: '600',
    color: theme.stepAwardTextColor,
  },
  amountText: {
    color: theme.stepAwardTextColor,
  },
  amountTextActive: {
    color: theme.stepAwardTextColorActive,
  },
  finishBg: {
    width: 33,
    height: 33,
    marginLeft: 'auto',
    marginRight: 'auto',
  }
});
