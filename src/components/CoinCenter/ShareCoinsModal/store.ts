import { atom } from 'jotai';

import storage from "../../../storage";
import { atomWithStorage, createJSONStorage } from 'jotai/utils';

const nativeStorage = createJSONStorage(() => ({
  setItem: storage.set,
  getItem: storage.get,
  removeItem: storage.remove,
}))

export const modalShowDateAtom = atomWithStorage('shareCoinsModalShowDate', undefined, nativeStorage);
export const coinsModalShowAtom = atom<boolean | undefined>(undefined);
export const coinsModalExposeAtom = atom<boolean | undefined>(false);
