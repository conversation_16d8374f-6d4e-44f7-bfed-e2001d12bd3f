import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';
import { icon_sharecoins_finish } from '../../../../appImagesV2/share_coins_modal'


export const darkTheme = {
  bodyBgColor: '#FCFCFF',
  bodyBgColorActive: '#FCFCFF',
  bodyBgColorFinish: '#2E1C1E',
  stepTextColor: '#8380B0',
  stepTextColorActive: '#FFEFF1',
  stepTextColorFinish: '#6F4D50',
  stepHeadBg: {
    angle: 270,
    colors: ['#E7ECF6', '#E7ECF6'],
  },
  stepHeadBgOpacity: .7,
  stepHeadBgActive: {
    angle: 90,
    colors: ['#FF4646', '#FF1B54'],
    locations: [0, 0.99]
  },
  stepHeadBgFinish: {
    angle: 94,
    colors: ['#3B292B', '#3D2527'],
    locations: [0.04, 0.99]
  },
  stepContentBgColor: '#FFE5E4',
  stepContentBgColorActive: '#6F2D34',
  stepContentBgColorFinish: '#2E1C1E',
  stepBorderColor: '#E7ECF6',
  stepBorderColorActive: '#FF4646',
  stepBorderColorFinish: '#503437',
  stepFinishBg: icon_sharecoins_finish,
  stepAwardTextColor: '#8380B0',
  stepAwardTextColorActive: '#DF6774',
}

const lightTheme = {
  bodyBgColor: '#FCFCFF',
  bodyBgColorActive: '#FAFAFA',
  bodyBgColorFinish: '#FFF8F8',
  stepTextColor: '#8380B0',
  stepTextColorActive: '#FFF',
  stepTextColorFinish: '#F0A4A4',
  stepHeadBg: {
    angle: 270,
    colors: ['#E7ECF6', '#E7ECF6'],
  },
  stepHeadBgOpacity: 1,
  stepHeadBgActive: {
    angle: 90,
    colors: ['#FF4646', '#FF1B54'],
    locations: [0, 0.99]
  },
  stepHeadBgFinish: {
    angle: 100,
    colors: ['#FFE5E4', '#FFE5E4'],
    locations: [0.01, 0.99]
  },
  stepContentBgColor: '#FFE5E4',
  stepContentBgColorActive: '#FAFAFA',
  stepContentBgColorFinish: '#FFF8F8',
  stepBorderColor: '#E7ECF6',
  stepBorderColorActive: '#FF4250',
  stepBorderColorFinish: '#FFE5E4',
  stepFinishBg: icon_sharecoins_finish,
  stepAwardTextColor: '#8380B0',
  stepAwardTextColorActive: '#FF424F',
}

const awardThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default awardThemeStyleAtom;