import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';
import { bg_sharecoins_modal_dark, bg_sharecoins_modal_light, icon_sharecoins_close } from '../../../../appImagesV2/share_coins_modal'


export const darkTheme = {
  modalBg: bg_sharecoins_modal_dark,
  modalBgColor: '#282828',
  // bodyBgColor: '#131313',
  titleColor: '#DCDCDC',
  titleGradientColors: ['#C8D1E0', '#FFFFFF', '#C8D1E0'],
  titleGradientLocations: [0, 0.5, 1],
  subTitleColor: '#8D8D91',
  btnBg: {
    angle: 90,
    colors: ['#FF4444', '#FF4444'],
    locations: [0, 1]
    // backgroundColor: '#FF4444',
    // borderRadius: 120,
  },
}

const lightTheme = {
  modalBg: bg_sharecoins_modal_light,
  modalBgColor: '#FFFFFF',
  // bodyBgColor: '#FFF',
  titleColor: '#2C2C3C',
  titleGradientColors: ['#982D35', '#2B2B3B', '#2D3C60'],
  titleGradientLocations: [0, 0.5, 1],
  subTitleColor: 'rgba(44, 44, 60, 0.4)',
  btnBg: {
    angle: 89,
    colors: ['#FF4444', '#FF4444'],
    locations: [0, 0.99],
    // backgroundColor: '#FF4444',
    // borderRadius: 120,
  },
}

const themeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default themeStyleAtom;