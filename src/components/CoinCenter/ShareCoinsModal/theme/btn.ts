import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  light: 'https://imagev2.xmcdn.com/storages/c106-audiofreehighqps/6D/10/GKwRIUEKuaCpAAAK3AMPSSlO.png',

}

const lightTheme = {
  light: 'https://imagev2.xmcdn.com/storages/611d-audiofreehighqps/26/DB/GKwRIJIKuaCoAAAb8gMPSSkb.png',
}

const btnThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default btnThemeStyleAtom;