import { themeAtom } from '../../../../atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  bg: {
    angle: 150,
    colors: ['#521C23', '#3B1D1F'],
    locations: [.03, .96]
  },
  headBg: 'https://imagev2.xmcdn.com/storages/1b8b-audiofreehighqps/2C/FF/GAqhw84KxvouAAAqkQMUJhmg.png',
  headIcon: 'https://imagev2.xmcdn.com/storages/8d30-audiofreehighqps/BB/FF/GAqhpOoKxvixAABMgwMUJQSL.png',
  titleIcon: 'https://imagev2.xmcdn.com/storages/907e-audiofreehighqps/3F/06/GAqhDJEK_fliAAAKPgMpkHc2.png',
  subtitleColor: 'rgba(255,255,255,0.5)',
  awardBoxTitleBg: 'https://imagev2.xmcdn.com/storages/a5e3-audiofreehighqps/08/24/GAqhav0Kx9s9AAAFXwMUeIS7.png',
  awardBoxTitleColor: '#E3D6D7',
  awardBoxSubtitleColor: '#666666',
  foldArrow: 'https://imagev2.xmcdn.com/storages/064c-audiofreehighqps/B6/77/GKwRIasKyCdPAAAAoAMUmdU2.png',
  btnTextColor: '#FFEFF1',
  bodyBgColor: '#1B1B1B',
  btnBg: {
    angle: 90,
    colors: ['#AF4955', '#B2474F'],
    locations: [0, 1],
    // backgroundColor: '#FF4444',
    // borderRadius: 120,
  },
  subBtnBgColor: '#6F2D36',
  subBntTextColor: '#FF7D8B',
  disabledBtnBg: {
    angle: 90,
    colors: ['rgba(175, 73, 85, .6)', 'rgba(178, 71, 79, .6)'],
    locations: [0, 1]
  },
  award: {}
}

const lightTheme = {
  bg: {
    angle: 179,
    colors: ['#FF535C', '#FF8185', '#FF9396', '#FFCACC'],
    locations: [0, .43, .71, .99]
  },
  headBg: 'https://imagev2.xmcdn.com/storages/c830-audiofreehighqps/34/E4/GKwRIMAKxvouAABNFwMUJhoX.png',
  headIcon: 'https://imagev2.xmcdn.com/storages/5a3a-audiofreehighqps/47/84/GKwRIW4Kxvi9AABIFwMUJQyW.png',
  titleIcon: 'https://imagev2.xmcdn.com/storages/ba8c-audiofreehighqps/87/FF/GKwRIMAK_fshAAALjQMpkSdF.png',
  subtitleColor: 'rgba(255,255,255,0.8)',
  awardBoxTitleBg: 'https://imagev2.xmcdn.com/storages/8be8-audiofreehighqps/5B/71/GKwRIUEKx9s-AAAEQgMUeIUA.png',
  awardBoxTitleColor: '#FFFFFF',
  awardBoxSubtitleColor: '#999999',
  foldArrow: 'https://imagev2.xmcdn.com/storages/c1b7-audiofreehighqps/37/B6/GKwRIJIKyCdaAAAAoAMUmdhm.png',
  btnTextColor: '#FFFFFF',
  bodyBgColor: '#FFF',
  btnBg: {
    angle: 90,
    colors: ['#FF6C7E', '#FF4A5C'],
    locations: [0, .99]
  },
  subBtnBgColor: '#FFEAEB',
  subBntTextColor: '#FF5562',
  disabledBtnBg: {
    angle: 90,
    colors: ['#FF95A0', '#FF8590'],
    locations: [0, .99]
  },
  award: {
    bodyBgColor: '#FFF',
  }
}

const bannerThemeStyleAtom = atom((get) => {
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
})

export default bannerThemeStyleAtom;