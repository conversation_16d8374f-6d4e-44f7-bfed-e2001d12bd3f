import { useSet<PERSON><PERSON> } from "jotai";
import { writeShareConinsTaskAtom } from "../../ShareCoins/store";
import watchAd from 'utils/watchAd'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { Toast } from '@xmly/rn-sdk'
import { FallbackReqType } from 'constants/ad'
import { AD_POSITION, AD_SOURCE, RewardType } from 'constants/ad'

export default function usePlayAd() {
  // const taskInfo = useAtomValue(shareConinsTaskAtom);
  // const modalInfo = taskInfo?.shareCoinsModalInfo

  const fetchShareCoinsTask = useSetAtom(writeShareConinsTaskAtom)
  const rewardGoldCoin = useRewardGoldCoin()

  // const award = modalInfo?.dailyCoinsAwardDetail
  // const cashBalance = useSelector((state: RootState) => state.goldCoin.balance);

  /**
   * 观看广告并领取奖励金币
   *
   * 1. 调用 watchAd 拉起广告，等待用户观看完成。
   * 2. 广告观看成功后，调用 rewardGoldCoin 领取奖励金币。
   * 3. 如果领取成功，刷新任务数据，否则弹出失败提示。
   * 4. 捕获并处理所有异常，确保用户有失败提示。
   * @returns {Promise<void>}
   */
  async function handleWatchAd() {
    try {
      const res = await watchAd({
        sourceName: AD_SOURCE.SHARE_COINS,
        positionName: AD_POSITION.positionName,
        slotId: AD_POSITION.slotId,
        rewardType: RewardType.SHARE_COINS,
        coins: 0,
        rewardVideoStyle: 0,
      })
      if (res.success) {
        const result = await rewardGoldCoin({
          rewardType: RewardType.SHARE_COINS,
          sourceName: AD_SOURCE.SHARE_COINS,
          coins: 0, //  自己传
          adId: res.adId, //  广告给
          adResponseId: res.adResponseId, // 广告给
          encryptType: res.encryptType, // 广告给
          ecpm: res.ecpm, // 广告给
          fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
        })
        if (result?.success) {
          fetchShareCoinsTask()
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  return { handleWatchAd };

}