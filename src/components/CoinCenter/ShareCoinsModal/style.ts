import { StyleSheet, Platform } from 'react-native';
import { px } from 'utils/px';
import type { darkTheme } from './theme/modal';

export const gap = 8;
export const bigAward = 'https://imagev2.xmcdn.com/storages/d53c-audiofreehighqps/C3/E0/GKwRIDoKm2wYAAAGOgMDcjlg.png';

export const containerWidth = 303;
export const containerHeight = 343;
export const closeIcon = 'https://imagev2.xmcdn.com/storages/b9cc-audiofreehighqps/76/49/GKwRIMAKmw2-AAABCAMDQcYg.png';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    width: containerWidth,
    height: containerHeight,
    borderRadius: 16,
    position: 'relative',
    padding: 8,
    backgroundColor: theme.modalBgColor,
  },
  bg: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: containerWidth,
    height: px(103),
    borderRadius: 16,
    // borderTopStartRadius: 16,
    // borderTopEndRadius: 16,
  },
  markIcon: {
    position: 'absolute',
    top: 0,
    left: 27,
    zIndex: 0
  },
  markIconImage: {
    width: px(80),
    height: px(70),
  },

  head: {
    paddingTop: 22,
    paddingBottom: 17,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 22,
    fontWeight: Platform.OS === 'ios' ? 'bold' : '700',
    lineHeight: 30,
    fontFamily: Platform.OS === 'ios' ? 'PingFangSC-Semibold' : 'PingFang SC',
    color: theme.titleColor
  },
  subTitle: {
    fontSize: 13,
    lineHeight: 20,
    fontFamily: 'PingFang SC',
    color: theme.subTitleColor,
  },
  close: {
    position: 'absolute',
    top: 0,
    right: 0,
    padding: 12
  },
  closeIcon: {
    width: px(24),
    height: px(24),
  },
  body: {
    borderRadius: 5,
    paddingHorizontal: 4,
    paddingTop: 4,
    paddingBottom: 8,
    flex: 1,
  },
  btnWrapper: {
    marginHorizontal: gap,
    marginTop: 8,
  },
  btn: {
    width: px(237),
    height: px(48),
    // aspectRatio: 263 / 46,
    marginLeft: 'auto',
    marginRight: 'auto',
    marginTop: px(16),
    marginBottom: px(30),
    position: 'relative',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 120,
    backgroundColor: '#FF4444',
  },
  btnDisabled: {
    opacity: 0.5,
  },
  btnTitle: {
    color: '#FFF',
    fontSize: 16,
    fontFamily: 'PingFang SC',
    fontWeight: 'bold',
  }
});
