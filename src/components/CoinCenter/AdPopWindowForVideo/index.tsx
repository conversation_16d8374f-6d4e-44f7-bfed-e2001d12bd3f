import React, { useEffect, useState, useCallback, useImperativeHandle, forwardRef } from 'react'
import { View, Text, Platform, PixelRatio, Image } from 'react-native'
import Modal from './Modal'
import useThrottleCallback from 'hooks/useThrottleCallback'
import { Touch } from '@xmly/rn-components'
import { getStyles } from './styles'
import { useAtomValue } from 'jotai'
import signInThemeStyleAtom from './theme'
import LinearGradient from 'react-native-linear-gradient'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import { FallbackReqType, AD_SOURCE, RewardType } from 'constants/ad'
import { Toast } from '@xmly/rn-sdk'
// import { Svga } from "components/Svga";
import NativeInspireAdView from '../../common/InspireAdView/index'
import bg_credit_reward from 'appImagesV2/bg_credit_reward'
import { bg_ribbon_left, bg_ribbon_right } from 'appImagesV2/bg_credit_reward'
import { NativeEventEmitter, NativeModules } from 'react-native'
import { px } from 'utils/px'
import LottieView from 'lottie-react-native';
import xmlog from "utilsV2/xmlog";

const { InspireAd } = NativeModules

const AdPopWindowaForVideo = forwardRef(
  (
    {
      adHeight,
      setAdHeight,
      onNextVideo,
      onFinishVidoeReward,
    }: {
      adHeight: number | undefined
      setAdHeight: (height: number | undefined) => void;
      onNextVideo: () => void;
      onFinishVidoeReward: () => void;
    },
    ref
  ) => {
    const theme = useAtomValue(signInThemeStyleAtom)
    const styles = getStyles(theme)

    const rewardGoldCoinHook = useRewardGoldCoin()
    // 记录是否点击了视频第一次
    const [hasWatchVideoFirst, setHasWatchVideoFirst] = useState<boolean>(false)

    // 弹窗状态
    const [showAdModal, setShowAdModal] = useState(false);

    // 自增长operationId 
    const [operationId, setOperationId] = useState<number>(1);

    // 添加奖励提示状态
    const [showRewardToast, setShowRewardToast] = useState(false)
    // 记录视频奖励
    const [videoAward, setVideoAward] = useState<{
      retry: boolean
      toast: string
      coins: number
      upgrradeCoins: number
      adAgainCoins: number
    }>()

    useImperativeHandle(ref, () => ({
      finishVideo(adId?: number | undefined, adResponseId?: number, rewardCoin?: number, fallbackReq?: number) {
        handleVideoRewards(adId, adResponseId, rewardCoin, undefined, undefined, fallbackReq);
        setHasWatchVideoFirst(true)
      },
    }))

    // 点击下一条
    const nextVideo = (dialogType: string) => {
      onNextVideo();
      handleClose();
      xmlog.event(67706, 'dialogClick', { currPage: 'welfareCenter', '极速版'+dialogType, Item: '立即观看下一条' });
      console.log('点击下一条')
    }
    const onAdClick = () => {
      xmlog.event(67706, 'dialogClick', { currPage: 'welfareCenter', '极速版'+dialogType: '额外奖励', Item: '点击并浏览' });

    }
    // 添加高度转换函数
    const convertNativeHeightToRN = (nativeHeight: number) => {
      if (Platform.OS === 'ios') {
        // iOS: points 到 pixels 的转换
        // 1. 获取设备的像素密度比
        const pixelRatio = PixelRatio.get()
        // 2. 将 points 转换为 pixels
        const pixels = nativeHeight / pixelRatio
        console.log('pixelRatio=', pixelRatio, ', nativeHeight=', nativeHeight, ', pixels=', pixels)
        // 3. 将 pixels 转换为 React Native 的 px
        // return px(pixels);
        return pixels + 20
      } else {
        // Android: pixels 到 dp 的转换
        const pixelRatio = PixelRatio.get()
        // 将像素转换为 dp
        const dpHeight = nativeHeight / pixelRatio
        // 将 dp 转换为 px
        return px(dpHeight)
      }
    }

    const handleVideoRewards = useThrottleCallback(async (adId?: number, adResponseId?: number, rewardCoin: number = 0, ecpm: string = '', encryptType: string = '1', fallbackReq?: number) => {
      try {
        //  视频奖励
        const res = await rewardGoldCoinHook({
          rewardType: RewardType.AD_VIDEO,
          coins: rewardCoin,
          sourceName: AD_SOURCE.AD_VIDEO,
          adId: adId || undefined,
          adResponseId: adResponseId || undefined,
          encryptType,
          ecpm,
          fallbackReq: fallbackReq || FallbackReqType.NORMAL,
          extMap: JSON.stringify({
            source: AD_SOURCE.INCENTIVE_TASK,
          }),
        }, true)
        console.log('视频奖励', res);
        if (res.success == true) {
          setVideoAward({
            retry: res.retry,
            toast: res.toast,
            coins: res.coins,
            upgrradeCoins: res.upgradeCoins,
            adAgainCoins: res.adAgainCoins,
          })
          onFinishVidoeReward();
          if (res.upgradeCoins) {
            setOperationId(operationId + 1);
            setShowAdModal(true)
            setShowRewardToast(false)
            return;
          }
          setShowAdModal(false);
          setShowRewardToast(true);
          xmlog.event(67705, 'dialogView', { currPage: 'welfareCenter', dialogType: '极速版任务完成' });
          return;
        }
        Toast.info(res.toast || '领取奖励失败，请稍后重试');
        setShowAdModal(false)
        setShowRewardToast(false)
      } catch (error) {
        console.error('处理广告奖励失败:', error)
        Toast.info('领取奖励失败，请稍后重试')
      }
    })

    const handleExtraRewards = useThrottleCallback(async (adId?: number, adResponseId?: number, rewardCoin: number = 0, ecpm: string = '', encryptType: string = '1', fallbackReq?: number) => {
      try {
        //  换端奖励
        console.log('换端奖励');
        const res = await rewardGoldCoinHook({
          rewardType: RewardType.EXTRA_REWARDS,
          coins: rewardCoin,
          sourceName: AD_SOURCE.INCENTIVE_TASK,
          adId: adId || undefined,
          adResponseId: adResponseId || undefined,
          encryptType,
          ecpm,
          fallbackReq: fallbackReq || FallbackReqType.NORMAL,
          extMap: JSON.stringify({
            source: AD_SOURCE.INCENTIVE_TASK,
          }),
        }, true)
        console.log('换端奖励', res);
        if (res.success == true) {
          setVideoAward({
            retry: res.retry,
            toast: res.toast,
            coins: res.coins,
            upgrradeCoins: res.upgradeCoins,
            adAgainCoins: res.adAgainCoins,
          })
          setShowAdModal(false)
          setShowRewardToast(true)
          xmlog.event(67705, 'dialogView', { currPage: 'welfareCenter', dialogType: '极速版任务完成' });
          return
        }
        Toast.info(res.toast || '领取奖励失败，请稍后重试');
        // setShowAdModal(false)
        // setShowRewardToast(true)
      } catch (error) {
        console.error('处理广告奖励失败:', error)
        Toast.info('领取奖励失败，请稍后重试')
      }
    })

    // 添加事件监听
    useEffect(() => {
      console.log('NativeModules.InspireAd', NativeModules.InspireAd);
      // 检查 InspireAd 模块是否存在
      if (!NativeModules.InspireAd) {
        setShowAdModal(false)
        setShowRewardToast(false)
        return
      }

      try {
        // 创建事件发射器
        const eventEmitter = new NativeEventEmitter(InspireAd)
        // 监听奖励成功事件
        const rewardSuccessSubscription = eventEmitter?.addListener('onRewardSuccess', (event) => {
          console.log('收到站外奖励成功事件:', event)
          if (event.sourceName === AD_SOURCE.INCENTIVE_TASK) {
            handleExtraRewards(event.adId, event.adResponseId, event.rewardCoin, event.ecpm, event.encryptType, event.fallbackReq)
          }
        })

        // 监听奖励失败事件
        const rewardFailSubscription = eventEmitter?.addListener('onRewardFail', (event) => {
          console.log('收到原生奖励失败事件:', event)
          if (event.sourceName === AD_SOURCE.INCENTIVE_TASK) {
            // setShowAdModal(true)
            // setShowRewardToast(false)
          }
        })

        // 清理订阅
        return () => {
          console.log('清理订阅')
          rewardSuccessSubscription?.remove()
          rewardFailSubscription?.remove()
        }
      } catch (error) {
        console.error('设置事件监听器失败:', error)
        // 降级处理：直接显示奖励提示
        setShowAdModal(false)
        setShowRewardToast(false)
        return () => { }
      }
    }, [])

    // 处理高度变化
    const handleHeightChange = useCallback(
      (e: any) => {
        if (!hasWatchVideoFirst) {
          return
        }
        try {
          const nativeHeight = e.nativeEvent?.height
          console.log('nativeHeight', nativeHeight)
          if (typeof nativeHeight === 'number' && nativeHeight > 0) {
            setAdHeight(nativeHeight)
            // setShowAdModal(true)
            // setShowRewardToast(false)
            xmlog.event(67705, 'dialogView', { currPage: 'welfareCenter', dialogType: '极速版额外奖励' });
            console.log('handleHeightChange setAdHeight', nativeHeight)
          } else {
            setAdHeight(0)
            setShowAdModal(false)
            setShowRewardToast(true)
            xmlog.event(67705, 'dialogView', { currPage: 'welfareCenter', dialogType: '极速版任务完成' });
            // setShowAdModal(true)
            // setShowRewardToast(false)
          }
        } catch (error) {
          console.error('处理高度变化错误:', error)
          setAdHeight(0)
          setShowAdModal(false)
        }
      },
      [handleVideoRewards]
    )

    // 渲染奖励提示
    const renderRewardToast = () => {
      if (!showRewardToast) return null
      return (
        <View style={[styles.rewardToastContent, videoAward?.adAgainCoins ? styles.rewardToastContentBig : styles.rewardToastContentMin]}>
          <Touch
            style={styles.closeButton}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            onPress={() => handleClose()}
          >
            <Image
              source={theme.iconClose}
              height={16}
              width={16}
              style={styles.closeIcon}
            />
          </Touch>
          <LinearGradient
            colors={['rgba(226, 160, 160, 0.2)', 'rgba(226, 160, 160, 0)']}
            style={styles.windowGradient}
          />
          <LottieView
            source={bg_ribbon_left}
            autoPlay
            loop={false}
            style={styles.toastRibbonLeft}
          />
          <View style={styles.rewardToastTitleContainer}>
            <Text style={styles.rewardToastTitle}>恭喜获得</Text>
            <View style={styles.rewardToastSubTitleContainer}>
              <Text style={styles.rewardToastLeftSubTitle}>{videoAward?.coins}</Text>
              <Text style={styles.rewardToastRightSubTitle}>金币</Text>
            </View>
          </View>
          <LottieView
            source={bg_ribbon_right}
            autoPlay
            loop={false}
            style={styles.toastRibbonRight}
          />
          <Touch
            style={styles.rewardToastButton}
            onPress={() => {
              setShowRewardToast(false)
              xmlog.event(67706, 'dialogClick', { currPage: 'welfareCenter', dialogType: '极速版任务完成', Item: '我知道了' });

            }}
          >
            <Text style={styles.rewardToastButtonText}>我知道了</Text>
          </Touch>
          <Touch
            style={styles.nextVideo}
            onPress={() => nextVideo('任务完成')}
          >
            {
              videoAward?.adAgainCoins ? <Text style={styles.nextVideoText}>点击进入下一条</Text> : null
            }

          </Touch>
        </View>
      )
    }

    // 渲染广告弹窗
    const renderAdModal = () => {
      if (!showAdModal) {
        return null
      }
      return (
        <View style={[styles.modalContent, adHeight && adHeight > 0 ? styles.modalContentVisible : styles.modalContentInitial]}>
          <Touch
            style={styles.closeButton}
            hitSlop={{ top: 12, bottom: 12, left: 12, right: 12 }}
            onPress={() => handleClose()}
          >
            <Image
              source={theme.iconClose}
              height={16}
              width={16}
              style={styles.closeIcon}
            />
          </Touch>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              恭喜获得 <Text style={styles.coinText}>{videoAward?.coins}</Text> 金币
            </Text>
          </View>
          <View style={styles.adRewardContainer}>
            <LottieView
              source={bg_ribbon_left}
              autoPlay
              loop={false}
              style={styles.ribbonLeft}
            />
            <View style={styles.rewardContainer}>
              <Image
                source={bg_credit_reward}
                style={styles.rewardBg}
              />
              <View style={styles.rewardTextContainer}>
                <View style={styles.rewardItem}>
                  <Text style={styles.rewardTextLeft}>已获取</Text>
                  <Text style={styles.rewardTextLeft}>{videoAward?.coins}金币</Text>
                </View>
                <View style={styles.rewardItem}>
                  <Text style={styles.rewardTextRight}>看广告领</Text>
                  <Text style={styles.rewardTextRight}>{(videoAward?.coins || 0) + (videoAward?.upgrradeCoins || 0)}金币</Text>
                </View>
              </View>
            </View>
            <LottieView
              source={bg_ribbon_right}
              autoPlay
              loop={false}
              style={styles.ribbonRight}
            />
          </View>
          <View
            style={[
              styles.adContainer,
              {
                height: adHeight > 0 ? convertNativeHeightToRN(adHeight) : 0,
              },
            ]}
          >
            <NativeInspireAdView
              style={[styles.adView]}
              positionName="fuliyehuanduanguanggao"
              operationId={String(operationId)}
              slotId={308}
              sourceName={AD_SOURCE.INCENTIVE_TASK}
              rewardCoin={videoAward?.upgrradeCoins || 0}
              onHeightChange={handleHeightChange}
              onAdClick={onAdClick}
            />
          </View>
          <Touch
            style={[styles.nextVideoAdView, Platform.OS === 'ios' ? styles.nextVideoAdViewIOS : styles.nextVideoAdViewAndroid]}
            onPress={() => nextVideo('额外奖励')}
          >
            {
              videoAward?.adAgainCoins ? <Text style={styles.nextVideoText}>点击进入下一条</Text> : null
            }

          </Touch>
        </View>
      )
    }

    // 处理关闭按钮点击
    const handleClose = () => {
      setShowAdModal(false)
      setShowRewardToast(false)
    }

    if (!hasWatchVideoFirst) {
      return null
    }
    return (
      <Modal
        visible={showAdModal || showRewardToast}
        overlayVisible={adHeight !== 0}
        onClose={handleClose}
      >
        {renderRewardToast()}
        {renderAdModal()}
      </Modal>
    )
  }
)
export default AdPopWindowaForVideo
