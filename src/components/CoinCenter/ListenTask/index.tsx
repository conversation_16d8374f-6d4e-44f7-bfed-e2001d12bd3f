import React, { useEffect, useRef, useState } from 'react'
import { View, Text, TouchableOpacity, ScrollView, Image } from 'react-native'
import { NativeModules } from 'react-native'
import { useFocusEffect } from '@react-navigation/native'
import LinearGradient from 'react-native-linear-gradient'
import { Toast, Page } from '@xmly/rn-sdk'
import { ScrollAnalyticComp } from '@xmly/react-native-page-analytics'
import { useAtomValue, useSetAtom } from 'jotai'
import { FallbackReqType } from 'constants/ad'
import { px } from 'utils/px'
import { listenTaskAtom, writeListenTaskAtom, initWelfareLifecycleListenerAtom, cleanupWelfareLifecycleListenerAtom } from './store'
import { showRewardModalAtom } from 'atom/listenTaskModal'
import watchAd from 'utils/watchAd'
import { LISTEN_TASK_POSITION, AD_SOURCE, RewardType } from 'constants/ad'
import { TaskStatus, encryptLocalDuration } from 'services/welfare/listenTask'
import useRewardGoldCoin from 'hooks/useRewardGoldCoin'
import xmlog from "utilsV2/xmlog";
import getXMRequestId from 'utilsV2/getXMRequestId'
import ModuleCard from '../common/ModuleCard'
import TaskButton from '../common/TaskButton'
import CoinAnimation from './CoinAnimation'
import listenTaskThemeAtom from './theme'
import { getStyles } from './styles'

const atMostIcon = 'https://imagev2.xmcdn.com/storages/a7d9-audiofreehighqps/22/96/GKwRIRwLwqBZAAAJPgONZwd5.png'
const finishedIcon = 'https://imagev2.xmcdn.com/storages/220a-audiofreehighqps/4E/72/GAqh_aQL19fZAAABDAOZ9DHC.png'



export const ListenTask: React.FC = () => {
  const listenTaskInfo = useAtomValue(listenTaskAtom)
  const fetchListenTask = useSetAtom(writeListenTaskAtom)
  const initWelfareLifecycleListener = useSetAtom(initWelfareLifecycleListenerAtom)
  const cleanupWelfareLifecycleListener = useSetAtom(cleanupWelfareLifecycleListenerAtom)
  const theme = useAtomValue(listenTaskThemeAtom)
  const rewardGoldCoin = useRewardGoldCoin()
  const scrollRef = useRef<ScrollView>(null)
  const [lastMarginRight, setLastMarginRight] = useState(10)
  const setModalInfo = useSetAtom(showRewardModalAtom)
  const hasReportedExposure = useRef<boolean>(false)
  const [isRendered, setIsRendered] = useState(false)
  const [isNetworkDataReady, setIsNetworkDataReady] = useState(false)

  // 初始化时获取收听任务数据并设置 WelfareLifecycle 监听器
  useEffect(() => {
    const initData = async () => {
      await fetchListenTask()
      setIsNetworkDataReady(true) // 标记网络数据已准备好
    }
    initData()
    initWelfareLifecycleListener()

    // 清理监听器
    return () => {
      cleanupWelfareLifecycleListener()
    };
  }, [])

  const styles = getStyles(theme)

  const onShow = () => {
    // 福利中心-收听任务  控件曝光
    xmlog.event(68288, 'slipPage',{currPage : 'welfareCenter', xmRequestId : XX, isDuplicateView : XX, taskTitle : XX, taskId : XX });
    
  }

  // 极速版-新福利中心-听书任务 控件曝光 (69270)
  useEffect(() => {
    const reportExposure = async () => {
      // 确保网络请求数据已经回来且成功，并且有完整的数据，且组件已渲染完成，且网络数据已准备好
      if (listenTaskInfo.success && 
          listenTaskInfo.status !== undefined && 
          listenTaskInfo.btnText !== undefined &&
          isRendered &&
          isNetworkDataReady) {
        try {
          const xmRequestId = await getXMRequestId()
          // 使用与页面渲染完全一致的逻辑
          const taskTitle = listenTaskInfo.status === TaskStatus.PENDING ? '去领取' : '去收听'
          
          xmlog.event(69270, 'slipPage', {
            taskTitle: taskTitle,
            taskId: 'listen_task',
            currPage: 'welfareCenter',
            xmRequestId: xmRequestId || '',
            exploreType: hasReportedExposure.current ? '2' : '1', // 1-首屏曝光, 2-返回页面后的控件曝光
            isDuplicateView: hasReportedExposure.current ? '1' : '0'
          })
        } catch (error) {
          console.error('报告听书任务曝光失败:', error)
        }
        
        // 标记已上报
        hasReportedExposure.current = true
      }
    }

    reportExposure()
  }, [listenTaskInfo.success, listenTaskInfo.status, isRendered, isNetworkDataReady])

  // 组件初始化时重置状态
  useEffect(() => {
    setIsRendered(false)
    setIsNetworkDataReady(false)
    hasReportedExposure.current = false
  }, [])

  function handleLayout(e: any) {
    console.log('handleLayout', e.nativeEvent.layout.width)
    const scrollWidth = e.nativeEvent.layout.width
    setLastMarginRight(Math.max(10, (scrollWidth - 60 * 6) / 6))
    
    // 标记组件布局完成，可以开始上报埋点
    if (!isRendered) {
      setIsRendered(true)
    }
  }

  const claimReward = async (amount?: number) => {
    try {
      clickReport(amount ? `${amount}` : '去领取')
      const res = await watchAd({
        sourceName: AD_SOURCE.LISTEN_TASK,
        positionName: LISTEN_TASK_POSITION.positionName,
        slotId: LISTEN_TASK_POSITION.slotId,
        rewardType: RewardType.LISTEN_TASK,
        coins: amount,
        rewardVideoStyle: 0,
        configPositionName: 'inspire_video_listen_book_rn',
        extInfo: JSON.stringify({sourceName: AD_SOURCE.LISTEN_TASK,})
      })
      if (res.success) {
        // 获取当前听书时长
        let currentListenDuration = 0;
        let encryptedListenDuration = '';
        try {
          const listenTimeRes = await NativeModules.ListenTime?.getListenDuration();
          currentListenDuration = listenTimeRes?.listenDuration || 0;
          console.info('debug_claimReward_listenDuration', '当前听书时长', currentListenDuration);

          // 使用 AES 加密听书时长
          encryptedListenDuration = encryptLocalDuration(currentListenDuration);
          console.info('debug_claimReward_encrypted_duration', '加密后的听书时长', encryptedListenDuration);
        } catch (error) {
          console.warn('debug_claimReward_listenDuration_error', '获取或加密听书时长失败', error);
        }

        const result = await rewardGoldCoin(
          {
            rewardType: RewardType.LISTEN_TASK,
            sourceName: AD_SOURCE.LISTEN_TASK,
            coins: amount || 0, //  自己传
            adId: res.adId, //  广告给
            adResponseId: res.adResponseId, // 广告给
            encryptType: res.encryptType, // 广告给
            ecpm: res.ecpm, // 广告给
            fallbackReq: res.fallbackReq ?? FallbackReqType.NORMAL, //广告给
            extMap: JSON.stringify({
              source: AD_SOURCE.LISTEN_TASK,
              listenDuration: encryptedListenDuration
            })
          },
          true
        )
        if (result?.success) {
          fetchListenTask()
          setModalInfo({
            showRewardModal: true,
            coins: result?.coins,
            upgradeCoins: result?.upgradeCoins,
            modalType: result?.upgradeCoins ? 'withAd' : 'normal',
          })
        } else {
          Toast.info('获取奖励失败')
        }
      }
    } catch (e) {
      Toast.info('获取奖励失败')
    }
  }

  const clickReport = (title: string) => {
    // 埋点已移除
  }

  // 极速版-新福利中心-听书任务-任务界面 点击事件 (69522)
  const clickReportTaskButton = async (item: string) => {
    try {
      const xmRequestId = await getXMRequestId()
      xmlog.click(69522, 'ListenTask', {
        item: item,
        currPage: 'welfareCenter',
        xmRequestId: xmRequestId || ''
      });
    } catch (error) {
      console.error('报告听书任务点击失败:', error)
    }
  }

  if (!listenTaskInfo.success) return null

  return (
    <ScrollAnalyticComp
      itemKey={'ListenTask'}
      onShow={onShow}
    >
      <ModuleCard style={styles.container}>
        <View>
          <View style={styles.header}>
            <View style={{ maxWidth: '80%' }}>
              <Text style={styles.title}>听书赚金币</Text>
              <Text
                numberOfLines={1}
                ellipsizeMode="tail"
                style={styles.totalCoins}
              >
                {listenTaskInfo.title}
              </Text>
            </View>
            {listenTaskInfo.status === TaskStatus.CLAIMED ? (
              <Text style={styles.textStyle}>明日再来</Text>
            ) : (
              <TaskButton
                text={listenTaskInfo.status === TaskStatus.PENDING ? '去领取' : '去收听'}
                onPress={async () => {
                  const buttonText = listenTaskInfo.status === TaskStatus.PENDING ? '去领取' : '去收听'
                  await clickReportTaskButton(buttonText)
                  
                  if (listenTaskInfo.status === TaskStatus.PENDING) {
                    claimReward()
                  } else {
                    clickReport('去收听')
                    Page.start('uting://open?msg_type=10002&navigation=subscribe')
                  }
                }}
              />
            )}
          </View>
          <ScrollView
            ref={scrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            bounces={false}
            scrollEventThrottle={16}
            onLayout={handleLayout}
          >
            {listenTaskInfo.stepInfo.map((step, index) => {
              const bgColor = step.status === TaskStatus.UNFINISHED ? theme.unfinishedDotColor : '#FF4444'
              const penddingIndex = listenTaskInfo.stepInfo.findIndex((item) => item.status === TaskStatus.PENDING)

              return (
                <View
                  key={index}
                  style={[
                    styles.stepItem,
                    index === listenTaskInfo.currentStep && styles.currentStep,
                    index === 0 && { marginLeft: px(16) },
                    index === listenTaskInfo?.stepInfo?.length - 1 && { marginRight: px(16) },
                    // step.status === 2 && styles.completedStep,
                  ]}
                >
                  <TouchableOpacity
                    onPress={async () => {
                      if (step.status === TaskStatus.PENDING) {
                        await clickReportTaskButton('去领取')
                        claimReward(step.amount)
                      }
                    }}
                  >
                    <View style={[styles.stepTag]}>
                      <View
                        style={[styles.coinBubble, step.status === TaskStatus.CLAIMED && styles.completedColor, step.status === TaskStatus.PENDING && styles.coinBubblePendding]}
                      >
                        {(step.status === TaskStatus.UNFINISHED || (step.status === TaskStatus.PENDING && index !== penddingIndex)) && (
                          <Image
                            source={{ uri: atMostIcon }}
                            key={atMostIcon}
                            style={styles.atMostIcon}
                          />
                        )}
                        {step.status === TaskStatus.PENDING && index === penddingIndex && <CoinAnimation loop />}
                        {step.status === TaskStatus.CLAIMED && (
                          <Image
                            source={{ uri: finishedIcon }}
                            key={finishedIcon}
                            style={styles.claimedIcon}
                          />
                        )}
                        <Text
                          style={[
                            styles.coinAmount,
                            step.status === TaskStatus.UNFINISHED && styles.coinAmountUnfinished,
                            step.status === TaskStatus.CLAIMED && styles.coinAmountClaimed,
                            styles.xmNumber,
                          ]}
                        >
                          +{step.amount}
                        </Text>
                      </View>
                      <View
                        style={[styles.triangle, step.status === TaskStatus.CLAIMED && styles.triangleCompleted, step.status === TaskStatus.PENDING && styles.trianglePendding]}
                      />
                    </View>
                  </TouchableOpacity>

                  <View style={styles.stepProgress}>
                    <View style={[styles.stepDot, { backgroundColor: bgColor }]} />
                    {index === 0 ? (
                      <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={['transparent', 'transparent', bgColor, bgColor]}
                        locations={[0, 0.5, 0.5, 1]}
                        style={styles.stepLine}
                      />
                    ) : index === listenTaskInfo?.stepInfo?.length - 1 ? (
                      <LinearGradient
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        colors={[bgColor, bgColor, 'transparent', 'transparent']}
                        locations={[0, 0.5, 0.5, 1]}
                        style={styles.stepLine}
                      />
                    ) : (
                      <View
                        style={[
                          styles.stepLine,
                          {
                            backgroundColor: bgColor,
                          },
                        ]}
                      />
                    )}
                  </View>
                  <View style={styles.stepInfo}>
                    <Text style={[styles.stepTime, step.status === TaskStatus.PENDING && styles.stepFinishTime, styles.xmNumber]}>{step.condition}分钟</Text>
                  </View>
                </View>
              )
            })}
          </ScrollView>
        </View>
      </ModuleCard>
    </ScrollAnalyticComp>
  )
}

export default ListenTask
