export interface MainAppTaskDetailRequest {
    sceneId: number; // 场景ID（首页为1，福利中心为2）
    installMainApp: boolean; // 是否安装主APP
}

export interface MainAppTaskDetailResponse {
    xmRequestId?: string | undefined; // 请求ID
    hasMainApp?: string | undefined; // 是否安装主APP

    taskId?: number; // 任务ID
    coinsNum?: number; // 金币数量
    showTask: boolean; // 是否展示任务
    modelTitle?: string; // 模块标题（福利页独有）
    jumpUrl?: string; // 跳转链接
    title?: string; // 主标题
    subTitle?: string; // 副标题
    buttonText?: string; // 按钮文案
    taskStatus?: number; // 任务状态 0-未完成 1-已完成 2-已领取
    icon?: string; // 图标地址（福利页独有
    lastReceiveCoinTs?: number; // 上次领取金币时间戳
}

export enum TaskStatus {
    NOT_COMPLETED = 0, // 未完成
    COMPLETED = 1, // 已完成
    CLAIMED = 2, // 已领取
}

export enum SceneId {
    HOME = 1, // 首页
    WELFARE_CENTER = 2, // 福利中心
}